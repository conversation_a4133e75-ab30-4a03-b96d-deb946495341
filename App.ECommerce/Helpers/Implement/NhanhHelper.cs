using App.Base.Repository.Entities;
using App.Base.Utilities;
using App.ECommerce.Helpers.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.NhanhDtos;
using App.ECommerce.Resource.Dtos.Webhooks;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using App.ECommerce.ProcessFlow.Interface;

using AutoMapper;

using MongoDB.Bson;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace App.ECommerce.Helpers;

public class NhanhHelper : INhanhHelper
{
    protected readonly ICryptoRepository _cryptoRepository;
    protected readonly ISyncServiceConfigRepository _syncConfigRepository;
    protected readonly ISyncServiceHelper _syncServiceHelper;
    protected readonly IMapper _mapper;
    protected readonly IItemsRepository _itemsRepository;
    protected readonly IOrderRepository _orderRepository;
    protected readonly ICategoryRepository _categoryRepository;
    protected readonly IShopRepository _shopRepository;
    protected readonly IUserRepository _userRepository;
    protected readonly IWarehouseRepository _warehouseRepository;
    protected readonly IItemsFlow _itemsFlow;

    public NhanhHelper(
        ICryptoRepository cryptoRepository,
        ISyncServiceConfigRepository syncConfigRepository,
        ISyncServiceHelper syncServiceHelper,
        IMapper mapper,
        IItemsRepository itemsRepository,
        IOrderRepository orderRepository,
        ICategoryRepository categoryRepository,
        IShopRepository shopRepository,
        IUserRepository userRepository,
        IWarehouseRepository warehouseRepository,
        IItemsFlow itemsFlow
    )
    {
        _cryptoRepository = cryptoRepository;
        _syncConfigRepository = syncConfigRepository;
        _syncServiceHelper = syncServiceHelper;
        _mapper = mapper;
        _itemsRepository = itemsRepository;
        _orderRepository = orderRepository;
        _categoryRepository = categoryRepository;
        _shopRepository = shopRepository;
        _userRepository = userRepository;
        _warehouseRepository = warehouseRepository;
        _itemsFlow = itemsFlow;
    }

    public async Task<Result<SyncServiceConfig>> SaveNhanhConfig(SyncServiceConfigDto dto)
    {
        try
        {
            // Mã hóa SecretKey trước khi lưu vào database
            string encryptedSecretKey = _cryptoRepository.Encrypt(dto.SecretKey);

            var config = new SyncServiceConfig
            {
                ShopId = dto.ShopId,
                SyncService = SyncServiceEnum.NhanhVN,
                AppId = dto.AppId,
                SecretKey = encryptedSecretKey,
                Status = TypeStatus.InActived,
                AdditionalConfig = dto.AdditionalConfig
            };

            var savedConfig = await _syncConfigRepository.CreateOrUpdate(config);
            return Result<SyncServiceConfig>.Success(savedConfig);
        }
        catch (Exception ex)
        {
            return Result<SyncServiceConfig>.Failure("SYNC_NHANH_CONFIG_SAVE_ERROR");
        }
    }
    public async Task<SyncServiceConfig> GetNhanhConfig(string shopId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        return config;
    }

    public async Task<Result<bool>> DeleteNhanhConfig(string shopId)
    {
        try
        {
            var config = await GetNhanhConfig(shopId);
            if (config == null) return Result<bool>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

            var deleted = await _syncConfigRepository.DeleteByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure("SYNC_NHANH_CONFIG_DELETE_ERROR");
        }
    }

    public async Task<Result<SyncServiceConfig>> UpdateNhanhAccessCode(string shopId, string accessCode)
    {
        var config = await GetNhanhConfig(shopId);
        if (config == null)
            return Result<SyncServiceConfig>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");
        config.SecretKey = _cryptoRepository.Decrypt(config.SecretKey);
        await _syncConfigRepository.UpdateAccessCode(shopId, SyncServiceEnum.NhanhVN, accessCode);

        var tokenResponse = await _syncConfigRepository.GetAccessTokenAsync(SyncServiceEnum.NhanhVN, config.AppId, accessCode, config.SecretKey) as NhanhAccessTokenResponseDto;
        if (!string.IsNullOrEmpty(tokenResponse?.AccessToken))
        {
            await _syncConfigRepository.UpdateAccessToken(shopId, SyncServiceEnum.NhanhVN, tokenResponse.AccessToken);
        }
        if (tokenResponse?.BusinessId != null)
        {
            await _syncConfigRepository.UpdateBusinessId(shopId, SyncServiceEnum.NhanhVN, tokenResponse.BusinessId);
        }
        if (string.IsNullOrEmpty(config.VerifyToken))
        {
            var verifyToken = TokenUtil.GenerateRandomVerifyToken();
            await _syncConfigRepository.UpdateVerifyToken(shopId, SyncServiceEnum.NhanhVN, verifyToken);
        }

        // Cập nhật trạng thái config thành actived sau khi hoàn thành tất cả các bước cập nhật
        await _syncConfigRepository.UpdateStatus(shopId, SyncServiceEnum.NhanhVN, TypeStatus.Actived);

        return Result<SyncServiceConfig>.Success(config);
    }

    public async Task<Result<bool>> SyncNhanhProductFromWebhook(object productData, string shopId)
    {
        NhanhProductWebhookDto nhanhProduct = JsonConvert.DeserializeObject<NhanhProductWebhookDto>(productData.ToString());
        Shop? shop = _shopRepository.FindByShopId(shopId);
        if (shop == null) return Result<bool>.Failure("SHOP_NOT_FOUND");

        // Gọi API để lấy thông tin chi tiết sản phẩm từ Nhanh.vn
        var productDetailResult = await GetNhanhProductByIdAsync(shopId, nhanhProduct.ProductId);
        if (!productDetailResult.IsSuccess)
            return Result<bool>.Failure($"SYNC_NHANH_GET_PRODUCT_ERROR");

        var productDetails = productDetailResult.Data;
        if (productDetails == null || !productDetails.Any())
            return Result<bool>.Failure("SYNC_NHANH_PRODUCT_DETAIL_EMPTY");

        // Lấy sản phẩm chính để kiểm tra parentId
        var mainProduct = productDetails.Values.FirstOrDefault();
        if (mainProduct == null)
            return Result<bool>.Failure("SYNC_NHANH_MAIN_PRODUCT_NOT_FOUND");

        // Xử lý logic dựa trên parentId
        if (mainProduct.ParentId > 0)
        {
            // parentId > 0: Sản phẩm con → Gọi API với parentId để lấy sản phẩm cha và tất cả variants
            var parentProductResult = await GetNhanhProductByIdAsync(shopId, mainProduct.ParentId);
            if (!parentProductResult.IsSuccess)
                return Result<bool>.Failure($"SYNC_NHANH_GET_PARENT_PRODUCT_ERROR");

            productDetails = parentProductResult.Data;
            if (productDetails == null || !productDetails.Any())
                return Result<bool>.Failure("SYNC_NHANH_PARENT_PRODUCT_DETAIL_EMPTY");

            // Cập nhật mainProduct thành sản phẩm cha
            mainProduct = productDetails.Values.FirstOrDefault(p => p.ParentId == NhanhConstants.ParentProductId);
            if (mainProduct == null)
                return Result<bool>.Failure("SYNC_NHANH_PARENT_PRODUCT_NOT_FOUND");
        }

        // Lấy categories từ Nhanh.vn
        var categories = await GetNhanhCategoriesAsync(shopId);
        var categoryObj = FindCategoryById(categories.Data, mainProduct.CategoryId);
        if (categoryObj == null)
            return Result<bool>.Failure("SYNC_NHANH_CATEGORY_NOT_FOUND");

        // Lấy category ID chính (sẽ tự động tạo category nếu chưa có)
        var categoryIds = await GetAllCategoryIdsFromHierarchy(categoryObj, shop);

        // Xử lý dựa trên loại sản phẩm
        if (mainProduct.ParentId == NhanhConstants.ParentProductId)
        {
            // Sản phẩm cha có variants: Tạo 1 ProductDto với ListVariant chứa tất cả variants
            var productDto = MapNhanhProductGroupToProductDto(productDetails, shopId, categoryIds);

            // Lấy ItemsCode từ Nhanh.vn (code của sản phẩm cha)
            string itemsCode = mainProduct.Code;

            // Kiểm tra xem sản phẩm đã tồn tại chưa bằng cách tìm theo ItemsCode
            var existingGroupItems = _itemsRepository.GroupByVariant(itemsCode);

            if (existingGroupItems != null)
            {
                // Update existing product group
                await UpdateExistingProductGroup(existingGroupItems, productDto, itemsCode);
            }
            else
            {
                // Create new product group
                await CreateNewProductGroup(productDto, itemsCode, mainProduct.IdNhanh.ToString());
            }
        }
        else
        {
            // Sản phẩm độc lập: Chỉ có 1 sản phẩm
            var productDto = MapNhanhProductDetailToProductDto(mainProduct, shopId, categoryIds);

            // Lấy ItemsCode từ Nhanh.vn
            string itemsCode = mainProduct.Code;

            // Kiểm tra xem sản phẩm đã tồn tại chưa
            var existingItem = _syncServiceHelper.FindItemByExternalId(shopId, mainProduct.IdNhanh.ToString(), SyncServiceEnum.NhanhVN);

            if (existingItem != null)
            {
                // Update existing product
                await UpdateExistingProduct(existingItem, productDto);
            }
            else
            {
                // Create new product
                await CreateNewProduct(productDto, itemsCode, mainProduct.IdNhanh.ToString());
            }
        }

        return Result<bool>.Success(true);
    }

    /// <summary>
    /// Xử lý webhook thay đổi tồn kho từ Nhanh.vn
    /// </summary>
    /// <param name="inventoryData">Dữ liệu inventory change từ webhook</param>
    /// <param name="shopId">ID của shop</param>
    /// <returns>Kết quả xử lý</returns>
    public async Task<Result<bool>> SyncNhanhInventoryFromWebhook(object inventoryData, string shopId)
    {
        try
        {
            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return Result<bool>.Failure("SHOP_NOT_FOUND");

            // Parse dữ liệu inventory change
            var inventoryDict = JsonConvert.DeserializeObject<Dictionary<string, NhanhInventoryChangeDto>>(inventoryData.ToString());
            if (inventoryDict == null || !inventoryDict.Any())
                return Result<bool>.Failure("SYNC_NHANH_INVENTORY_DATA_EMPTY");

            foreach (var inventoryKvp in inventoryDict)
            {
                var inventory = inventoryKvp.Value;

                // Tìm sản phẩm theo ExternalId (Nhanh product ID)
                var existingItem = _syncServiceHelper.FindItemByExternalId(shopId, inventory.Id.ToString(), SyncServiceEnum.NhanhVN);

                if (existingItem == null)
                {
                    // Nếu không tìm thấy theo ExternalId, thử tìm theo ItemsCode
                    var itemsByCode = _itemsRepository.FindByItemsCode(inventory.Code);
                    existingItem = itemsByCode.FirstOrDefault(i => i.ShopId == shopId && i.ExternalSource == SyncServiceEnum.NhanhVN);
                }

                if (existingItem != null)
                {
                    // Cập nhật quantity từ available
                    existingItem.Quantity = inventory.Available;
                    existingItem.Updated = DateTime.UtcNow;

                    _itemsRepository.UpdateItems(existingItem);

                    Console.WriteLine($"Updated inventory for product {inventory.Code}: {inventory.Available}");
                }
                else
                {
                    Console.WriteLine($"Product not found for inventory update: ID={inventory.Id}, Code={inventory.Code}");
                    // Có thể log hoặc tạo sản phẩm mới nếu cần
                }
            }

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error syncing inventory from Nhanh: {ex.Message}");
            return Result<bool>.Failure("SYNC_NHANH_INVENTORY_ERROR");
        }
    }

    public async Task<Result<bool>> SyncNhanhOrderFromWebhook(object orderData, string shopId)
    {
        NhanhOrderDataDto nhanhOrder = JsonConvert.DeserializeObject<NhanhOrderDataDto>(orderData.ToString());
        // Gọi lại API Nhanh.vn để lấy thông tin đầy đủ của đơn hàng
        var fullOrderResult = await GetNhanhOrderByIdAsync(shopId, nhanhOrder.OrderId);
        if (!fullOrderResult.IsSuccess)
            return Result<bool>.Failure($"SYNC_NHANH_GET_ORDER_ERROR: {string.Join(", ", fullOrderResult.Errors)}");

        var fullOrderData = fullOrderResult.Data;
        var existingOrder = _syncServiceHelper.FindOrderByExternalId(shopId, fullOrderData.Id.ToString(), SyncServiceEnum.NhanhVN);
        var nhanhCustomer = await GetNhanhCustomerByIdAsync(shopId, fullOrderData.CustomerId);
        User user = EnsureUserFromNhanhCustomer(nhanhCustomer.Data, shopId);

        // Sử dụng hàm map riêng để tạo Order từ NhanhOrderDetailDto (dữ liệu đầy đủ)
        var mappedOrder = MapNhanhOrderDetailToOrder(fullOrderData, shopId, user);
        if (mappedOrder == null)
            return Result<bool>.Failure("SYNC_NHANH_ORDER_MAPPING_ERROR");

        if (existingOrder != null)
        {
            // Cập nhật đơn hàng hiện có
            UpdateExistingOrder(existingOrder, mappedOrder);
            _orderRepository.UpdateOrder(existingOrder);
        }
        else
        {
            // Tạo đơn hàng mới
            await _orderRepository.CreateOrder(mappedOrder);
        }
        return Result<bool>.Success(true);
    }
    private User EnsureUserFromNhanhCustomer(NhanhSyncCustomerDto nhanhCustomer, string shopId)
    {
        User? user = null;
        if (!string.IsNullOrEmpty(nhanhCustomer.Mobile))
            user = _userRepository.FindByUserPhone(shopId, nhanhCustomer.Mobile);

        if (user == null)
        {
            user = new User
            {
                UserId = Guid.NewGuid().ToString(),
                ShopId = shopId,
                Email = nhanhCustomer.Email,
                PhoneNumber = nhanhCustomer.Mobile,
                Fullname = nhanhCustomer.Name,
                Address = nhanhCustomer.Address,
                Status = TypeStatus.Actived,
                Created = DateTime.Now,
            };
            _userRepository.CreateUser(user);
        }
        else
        {
            user.Email = nhanhCustomer.Email;
            user.Fullname = nhanhCustomer.Name;
            user.PhoneNumber = nhanhCustomer.Mobile;
            user.Address = nhanhCustomer.Address;
            user.Updated = DateTime.Now;
            _userRepository.UpdateUser(user);
        }
        return user;
    }
    private async Task<Result<NhanhSyncCustomerDto>> GetNhanhCustomerByIdAsync(string shopId, int customerId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        if (config == null || string.IsNullOrEmpty(config.AccessToken))
            return Result<NhanhSyncCustomerDto>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

        using var httpClient = new HttpClient();
        var urlCategory = NhanhConstants.ApiCustomer;

        var form = new MultipartFormDataContent
        {
            { new StringContent("2.0"), "version" },
            { new StringContent(config.AppId), "appId" },
            { new StringContent(config.BusinessId.ToString()), "businessId" },
            { new StringContent(config.AccessToken), "accessToken" },
            { new StringContent(JsonConvert.SerializeObject(new { id = customerId })), "data" }
        };

        var response = await httpClient.PostAsync(urlCategory, form);
        response.EnsureSuccessStatusCode();
        var json = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<NhanhCustomerSearchData>>(json);

        if (apiResponse.Code != 1)
            return Result<NhanhSyncCustomerDto>.Failure("SYNC_NHANH_CUSTOMER_API_ERROR");

        if (apiResponse.Data?.Customers == null || !apiResponse.Data.Customers.Any())
            return Result<NhanhSyncCustomerDto>.Failure("SYNC_NHANH_CUSTOMER_NOT_FOUND");

        // Lấy customer đầu tiên từ dictionary
        var customerDto = apiResponse.Data.Customers.Values.First();
        return Result<NhanhSyncCustomerDto>.Success(customerDto);
    }

    /// <summary>
    /// Gọi API Nhanh.vn để lấy thông tin đầy đủ của đơn hàng theo ID
    /// </summary>
    /// <param name="shopId">ID của shop</param>
    /// <param name="orderId">ID của đơn hàng trên Nhanh.vn</param>
    /// <returns>Thông tin đầy đủ của đơn hàng</returns>
    private async Task<Result<NhanhOrderDetailDto>> GetNhanhOrderByIdAsync(string shopId, int orderId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        if (config == null || string.IsNullOrEmpty(config.AccessToken))
            return Result<NhanhOrderDetailDto>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

        using var httpClient = new HttpClient();
        var urlOrder = NhanhConstants.ApiOrder;

        var form = new MultipartFormDataContent
        {
            { new StringContent("2.0"), "version" },
            { new StringContent(config.AppId), "appId" },
            { new StringContent(config.BusinessId.ToString()), "businessId" },
            { new StringContent(config.AccessToken), "accessToken" },
            { new StringContent(JsonConvert.SerializeObject(new { id = orderId })), "data" }
        };

        var response = await httpClient.PostAsync(urlOrder, form);
        response.EnsureSuccessStatusCode();
        var json = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<NhanhOrderSearchResponse>>(json);

        if (apiResponse.Code != 1)
            return Result<NhanhOrderDetailDto>.Failure("SYNC_NHANH_ORDER_API_ERROR");

        if (apiResponse.Data?.Orders == null || !apiResponse.Data.Orders.Any())
            return Result<NhanhOrderDetailDto>.Failure("SYNC_NHANH_ORDER_NOT_FOUND");

        // Lấy đơn hàng đầu tiên từ dictionary (vì chỉ query 1 đơn hàng theo ID)
        var orderDto = apiResponse.Data.Orders.Values.First();
        return Result<NhanhOrderDetailDto>.Success(orderDto);
    }
    public async Task<Result<bool>> SyncNhanhCustomerFromWebhook(object customerData, string shopId)
    {
        try
        {
            // TODO: Implement customer sync logic for NhanhVN
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure("SYNC_NHANH_CUSTOMER_SYNC_ERROR");
        }
    }

    public async Task<Result<bool>> DeleteNhanhProductsFromWebhook(object productIds, string shopId)
    {
        try
        {
            if (productIds is List<int> nhanhProductIds)
            {
                foreach (var productId in nhanhProductIds)
                {
                    var existingItem = _syncServiceHelper.FindItemByExternalId(shopId, productId.ToString(), SyncServiceEnum.NhanhVN);
                    if (existingItem != null)
                    {
                        _itemsRepository.DeleteItems(existingItem.ItemsId);
                    }
                }
            }
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure("SYNC_NHANH_PRODUCT_DELETE_ERROR");
        }
    }

    public async Task<Result<bool>> DeleteNhanhOrderFromWebhook(object orderData, string shopId)
    {
        try
        {
            var nhanhOrder = JsonConvert.DeserializeObject<NhanhOrderDataDto>(orderData.ToString());
            var existingOrder = _syncServiceHelper.FindOrderByExternalId(shopId, nhanhOrder.OrderId.ToString(), SyncServiceEnum.NhanhVN);

            if (existingOrder != null)
            {
                _orderRepository.DeleteOrder(existingOrder.OrderId);
            }

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure("SYNC_NHANH_ORDER_DELETE_ERROR");
        }
    }

    public async Task<Result<bool>> CreateOrderToNhanh(Order order, string shopId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        if (config == null || string.IsNullOrEmpty(config.AccessToken))
            return Result<bool>.Failure("SYNC_NHANH_SHOP_NOT_CONFIGURED");

        // Giải mã SecretKey để sử dụng
        config.SecretKey = _cryptoRepository.Decrypt(config.SecretKey);

        // Tạo data object cho order
        var orderData = new
        {
            id = order.OrderNo,
            type = order.StatusDelivery == TypeDelivery.ExpressDelivery ? "Shipping" : "Shopping",
            customerName = order.UserShippingAddress?.FullName,
            customerMobile = order.UserShippingAddress?.PhoneNumber,
            customerAddress = order.UserShippingAddress?.Address,
            customerCityName = order.UserShippingAddress?.ProvinceName,
            customerDistrictName = order.UserShippingAddress?.DistrictName,
            customerWardName = order.UserShippingAddress?.WardName,
            paymentMethod = order.TypePay == TypePayment.COD ? "COD" : "Online",
            moneyDiscount = order.VoucherPromotionPrice + order.VoucherTransportPrice,
            paymentCode = order.TransportOrderId,
            carrierId = 12,
            customerShipFee = order.TransportPrice,
            status = MapOrderStatusToNhanhStatus(order.StatusOrder),
            description = order.Notes,
            productList = order.ListItems?.Select(x =>
            {
                var item = _itemsRepository.FindByItemsId(x.ItemsId);
                return new
                {
                    id = x.ItemsId,
                    name = x.ItemsName,
                    code = x.ItemsCode,
                    idNhanh = item?.ExternalId ?? null,
                    quantity = x.Quantity,
                    price = x.Price,
                    type = "Product",
                    weight = x.ItemsWeight,
                    importPrice = x.PriceCapital,
                    description = x.ItemsInfo
                };
            }).ToList(),
        };

        try
        {
            using var httpClient = new HttpClient();

            // Sử dụng MultipartFormDataContent như trong curl command
            var form = new MultipartFormDataContent
            {
                { new StringContent("2.0"), "version" },
                { new StringContent(config.AppId), "appId" },
                { new StringContent(config.BusinessId.ToString()), "businessId" },
                { new StringContent(config.AccessToken), "accessToken" },
                { new StringContent(JsonConvert.SerializeObject(orderData)), "data" }
            };

            // Sử dụng đúng URL cho create order
            var response = await httpClient.PostAsync(NhanhConstants.ApiCreateOrder, form);

            if (!response.IsSuccessStatusCode)
                return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_HTTP_ERROR");

            var json = await response.Content.ReadAsStringAsync();
            if (string.IsNullOrWhiteSpace(json))
                return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_EMPTY_RESPONSE");

            var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<NhanhCreateOrderResponseDto>>(json);

            if (apiResponse?.Code != 1)
                return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_API_ERROR");

            // Lưu thông tin response vào Order entity
            if (apiResponse.Data != null)
            {
                order.ExternalSource = SyncServiceEnum.NhanhVN;
                order.ExternalId = apiResponse.Data.OrderId.ToString();
                _orderRepository.UpdateOrder(order);
            }

            return Result<bool>.Success(true);
        }
        catch (JsonException)
        {
            return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_INVALID_JSON");
        }
        catch (HttpRequestException)
        {
            return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_NETWORK_ERROR");
        }
        catch (Exception)
        {
            return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_API_ERROR");
        }
    }

    public async Task<Result<bool>> UpdateOrderToNhanh(Order order, string shopId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        if (config == null || string.IsNullOrEmpty(config.AccessToken))
            return Result<bool>.Failure("SYNC_NHANH_SHOP_NOT_CONFIGURED");

        // Kiểm tra xem order có ExternalId không (đã được tạo trên Nhanh.vn)
        if (string.IsNullOrEmpty(order.ExternalId))
            return Result<bool>.Failure("SYNC_NHANH_ORDER_NOT_SYNCED");

        // Giải mã SecretKey để sử dụng
        config.SecretKey = _cryptoRepository.Decrypt(config.SecretKey);

        // Tạo data object cho order update
        var orderData = new
        {
            id = order.OrderNo,
            orderId = order.ExternalId,
            status = MapOrderStatusToNhanhStatus(order.StatusOrder),
        };

        try
        {
            using var httpClient = new HttpClient();

            // Sử dụng MultipartFormDataContent như trong curl command
            var form = new MultipartFormDataContent
            {
                { new StringContent("2.0"), "version" },
                { new StringContent(config.AppId), "appId" },
                { new StringContent(config.BusinessId.ToString()), "businessId" },
                { new StringContent(config.AccessToken), "accessToken" },
                { new StringContent(JsonConvert.SerializeObject(orderData)), "data" }
            };

            // Sử dụng đúng URL cho update order
            var response = await httpClient.PostAsync(NhanhConstants.ApiUpdateOrder, form);

            if (!response.IsSuccessStatusCode)
                return Result<bool>.Failure("SYNC_NHANH_UPDATE_ORDER_HTTP_ERROR");

            var json = await response.Content.ReadAsStringAsync();
            if (string.IsNullOrWhiteSpace(json))
                return Result<bool>.Failure("SYNC_NHANH_UPDATE_ORDER_EMPTY_RESPONSE");

            var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<object>>(json);

            if (apiResponse?.Code != 1)
                return Result<bool>.Failure("SYNC_NHANH_UPDATE_ORDER_API_ERROR");

            // Cập nhật thời gian updated cho order
            order.Updated = DateTime.Now;
            _orderRepository.UpdateOrder(order);

            return Result<bool>.Success(true);
        }
        catch (JsonException)
        {
            return Result<bool>.Failure("SYNC_NHANH_UPDATE_ORDER_INVALID_JSON");
        }
        catch (HttpRequestException)
        {
            return Result<bool>.Failure("SYNC_NHANH_UPDATE_ORDER_NETWORK_ERROR");
        }
        catch (Exception)
        {
            return Result<bool>.Failure("SYNC_NHANH_UPDATE_ORDER_API_ERROR");
        }
    }

    public async Task<Result<List<NhanhProductCategoryDto>>> GetNhanhCategoriesAsync(string shopId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        if (config == null || string.IsNullOrEmpty(config.AccessToken))
            return Result<List<NhanhProductCategoryDto>>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

        using var httpClient = new HttpClient();
        var urlCategory = NhanhConstants.ApiCategory;

        var form = new MultipartFormDataContent
        {
            { new StringContent("2.0"), "version" },
            { new StringContent(config.AppId), "appId" },
            { new StringContent(config.BusinessId.ToString()), "businessId" },
            { new StringContent(config.AccessToken), "accessToken" }
        };

        var response = await httpClient.PostAsync(urlCategory, form);
        response.EnsureSuccessStatusCode();
        var json = await response.Content.ReadAsStringAsync();
        var obj = JObject.Parse(json);
        if ((int)obj["code"] != 1)
            return Result<List<NhanhProductCategoryDto>>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");
        var categories = obj["data"].ToObject<List<NhanhProductCategoryDto>>();
        return Result<List<NhanhProductCategoryDto>>.Success(categories);

    }

    /// <summary>
    /// Gọi API Nhanh.vn để lấy thông tin chi tiết sản phẩm theo ID
    /// Xử lý logic dựa trên parentId:
    /// - parentId = -1: Sản phẩm độc lập → Lấy dữ liệu product
    /// - parentId = -2: Sản phẩm cha → Lấy cả product và các biến thể (variants)
    /// - parentId > 0: Sản phẩm con → Bỏ qua (sẽ được xử lý khi sync sản phẩm cha)
    /// </summary>
    /// <param name="shopId">ID của shop</param>
    /// <param name="productId">ID của sản phẩm trên Nhanh.vn</param>
    /// <returns>Thông tin chi tiết sản phẩm và các variants nếu có</returns>
    private async Task<Result<Dictionary<string, NhanhProductDetailDto>>> GetNhanhProductByIdAsync(string shopId, int productId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        if (config == null || string.IsNullOrEmpty(config.AccessToken))
            return Result<Dictionary<string, NhanhProductDetailDto>>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

        using var httpClient = new HttpClient();
        var urlProductDetail = NhanhConstants.ApiProductDetail;

        var form = new MultipartFormDataContent
        {
            { new StringContent("2.0"), "version" },
            { new StringContent(config.AppId), "appId" },
            { new StringContent(config.BusinessId.ToString()), "businessId" },
            { new StringContent(config.AccessToken), "accessToken" },
            { new StringContent(productId.ToString()), "data" }
        };

        var response = await httpClient.PostAsync(urlProductDetail, form);
        response.EnsureSuccessStatusCode();
        var json = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<Dictionary<string, NhanhProductDetailDto>>>(json);

        if (apiResponse.Code != 1)
            return Result<Dictionary<string, NhanhProductDetailDto>>.Failure("SYNC_NHANH_PRODUCT_API_ERROR");

        if (apiResponse.Data == null || !apiResponse.Data.Any())
            return Result<Dictionary<string, NhanhProductDetailDto>>.Failure("SYNC_NHANH_PRODUCT_NOT_FOUND");

        return Result<Dictionary<string, NhanhProductDetailDto>>.Success(apiResponse.Data);
    }

    public NhanhProductCategoryDto FindCategoryById(List<NhanhProductCategoryDto> categories, int categoryId)
    {
        foreach (var category in categories)
        {

            if (category.Id == categoryId)
                return category;
            if (category.Childs != null && category.Childs.Count > 0)
            {
                var found = FindCategoryById(category.Childs, categoryId);
                if (found != null)
                    return found;
            }
        }
        return null;
    }

    public async Task<Category> CreateCategoryWithHierarchy(NhanhProductCategoryDto categoryDto, Shop shop, string parentCategoryId = null, int level = 1)
    {
        // Kiểm tra xem category đã tồn tại chưa
        var existingCategory = await _categoryRepository.FindByExternalId(shop.ShopId, categoryDto.Id.ToString(), SyncServiceEnum.NhanhVN);
        if (existingCategory != null)
            return existingCategory;

        // Nếu có ParentId trong Nhanh và chưa có parentCategoryId, tìm hoặc tạo parent category
        if (categoryDto.ParentId > 0 && string.IsNullOrEmpty(parentCategoryId))
        {
            var parentCategory = await _categoryRepository.FindByExternalId(shop.ShopId, categoryDto.ParentId.ToString(), SyncServiceEnum.NhanhVN);
            if (parentCategory == null)
            {
                // Tìm parent category trong danh sách categories từ Nhanh
                var categories = await GetNhanhCategoriesAsync(shop.ShopId);
                var parentCategoryDto = FindCategoryById(categories.Data, categoryDto.ParentId);
                if (parentCategoryDto != null)
                {
                    parentCategory = await CreateCategoryWithHierarchy(parentCategoryDto, shop, null, level - 1);
                }
            }
            parentCategoryId = parentCategory?.CategoryId;
        }

        // Tạo category mới
        var newCategory = new Category
        {
            CategoryId = Guid.NewGuid().ToString(),
            PartnerId = shop.PartnerId,
            CategoryName = categoryDto.Name,
            ShopId = shop.ShopId,
            ParentId = parentCategoryId,
            Image = new MediaInfo
            {
                MediaFileId = Guid.NewGuid().ToString(),
                Type = TypeMedia.IMAGE,
                Link = categoryDto.Image
            },
            CategoryType = TypeCategory.Product,
            CategoryLevel = level.ToString(),
            CategoryPosition = categoryDto.Order,
            Publish = TypeCategoryPublish.Publish,
            Active = TypeRuleActive.Actived,
            ExternalSource = SyncServiceEnum.NhanhVN,
            ExternalId = categoryDto.Id.ToString(),
            Created = DateTime.UtcNow,
            Updated = DateTime.UtcNow
        };

        var createdCategory = await _categoryRepository.CreateCategory(newCategory);

        // Tạo các child categories nếu có
        if (categoryDto.Childs != null && categoryDto.Childs.Count > 0)
        {
            foreach (var childDto in categoryDto.Childs)
            {
                await CreateCategoryWithHierarchy(childDto, shop, createdCategory.CategoryId, level + 1);
            }
        }

        return createdCategory;
    }
    public async Task<List<string>> GetAllCategoryIdsFromHierarchy(NhanhProductCategoryDto categoryObj, Shop shop)
    {
        var categoryIds = new List<string>();

        // Chỉ thêm category chính, không bao gồm children
        var mainCategory = await _syncServiceHelper.FindCategoryByExternalId(shop.ShopId, categoryObj.Id.ToString(), SyncServiceEnum.NhanhVN);
        if (mainCategory == null)
        {
            mainCategory = await CreateCategoryWithHierarchy(categoryObj, shop);
        }
        categoryIds.Add(mainCategory.CategoryId);

        return categoryIds;
    }

    public async Task<List<string>> GetChildCategoryIds(List<NhanhProductCategoryDto> childCategories, Shop shop)
    {
        var categoryIds = new List<string>();

        foreach (var childCategory in childCategories)
        {
            var category = await _syncServiceHelper.FindCategoryByExternalId(shop.ShopId, childCategory.Id.ToString(), SyncServiceEnum.NhanhVN);
            if (category == null)
            {
                category = await CreateCategoryWithHierarchy(childCategory, shop);
            }
            categoryIds.Add(category.CategoryId);

            // Đệ quy để lấy children của children
            if (childCategory.Childs != null && childCategory.Childs.Count > 0)
            {
                var subChildCategoryIds = await GetChildCategoryIds(childCategory.Childs, shop);
                categoryIds.AddRange(subChildCategoryIds);
            }
        }

        return categoryIds;
    }

    /// <summary>
    /// Cập nhật thông tin đơn hàng hiện có từ dữ liệu Nhanh.vn
    /// </summary>
    /// <param name="existingOrder">Đơn hàng hiện có trong hệ thống</param>
    /// <param name="mappedOrder">Đơn hàng đã được map từ Nhanh.vn</param>
    private void UpdateExistingOrder(Order existingOrder, Order mappedOrder)
    {
        // Cập nhật thông tin cơ bản
        existingOrder.Notes = mappedOrder.Notes;
        existingOrder.StatusOrder = mappedOrder.StatusOrder;
        existingOrder.StatusTransport = mappedOrder.StatusTransport;
        existingOrder.TransportPrice = mappedOrder.StatusOrder != TypeOrderStatus.Refund ||
         mappedOrder.StatusOrder != TypeOrderStatus.Failed ? mappedOrder.TransportPrice : existingOrder.TransportPrice;
        existingOrder.TotalAfterTax = mappedOrder.StatusOrder != TypeOrderStatus.Refund ||
         mappedOrder.StatusOrder != TypeOrderStatus.Failed ? mappedOrder.TotalAfterTax : existingOrder.TotalAfterTax;
        existingOrder.Price = mappedOrder.StatusOrder != TypeOrderStatus.Refund ||
         mappedOrder.StatusOrder != TypeOrderStatus.Failed ? mappedOrder.Price : existingOrder.Price;

        // Cập nhật địa chỉ giao hàng
        if (mappedOrder.UserShippingAddress != null && mappedOrder.StatusOrder != TypeOrderStatus.Refund &&
         mappedOrder.StatusOrder != TypeOrderStatus.Failed)
        {
            existingOrder.UserShippingAddress = mappedOrder.UserShippingAddress;
        }
        if (mappedOrder.StatusOrder != TypeOrderStatus.Refund &&
                 mappedOrder.StatusOrder != TypeOrderStatus.Failed)
        {
            // Cập nhật danh sách items
            existingOrder.ListItems = mappedOrder.ListItems;
        }
        // Cập nhật thời gian
        existingOrder.Updated = DateTime.Now;
    }



    /// <summary>
    /// Map dữ liệu đơn hàng đầy đủ từ Nhanh.vn API thành Order entity
    /// </summary>
    /// <param name="nhanhOrder">Dữ liệu đơn hàng đầy đủ từ Nhanh.vn API</param>
    /// <param name="shopId">ID của shop</param>
    /// <param name="user">Thông tin user đã được tạo/cập nhật</param>
    /// <returns>Order entity đã được map</returns>
    public Order MapNhanhOrderDetailToOrder(NhanhOrderDetailDto nhanhOrder, string shopId, User user)
    {
        Shop? shop = _shopRepository.FindByShopId(shopId);
        if (shop == null) return null;

        // Tạo danh sách items từ products trong đơn hàng
        var listItems = new List<ItemsOrder>();
        foreach (var prod in nhanhOrder.Products ?? new List<NhanhOrderProductDetailDto>())
        {
            var item = _syncServiceHelper.FindItemByExternalId(shopId, prod.ProductId?.ToString(), SyncServiceEnum.NhanhVN);
            if (item != null)
            {
                ItemsOrder itemsOrder = _mapper.Map<ItemsOrder>(item);
                itemsOrder.Quantity = int.TryParse(prod.Quantity, out var qty) ? qty : 0;
                itemsOrder.TotalBeforeTax = decimal.TryParse(prod.Price, out var price) ? price : 0;
                itemsOrder.TotalAfterTax = decimal.TryParse(prod.Price, out var priceAfter) ? priceAfter : 0;
                listItems.Add(itemsOrder);
            }
        }

        // Tạo địa chỉ giao hàng
        var shippingAddress = new ShippingAddress
        {
            UserId = user.UserId,
            ProvinceName = nhanhOrder.CustomerCity,
            DistrictName = nhanhOrder.CustomerDistrict,
            WardName = nhanhOrder.CustomerWard,
            Address = nhanhOrder.CustomerAddress,
            FullName = nhanhOrder.CustomerName,
            PhoneNumber = nhanhOrder.CustomerMobile,
        };

        // Tính tổng giá từ các items
        var totalPrice = listItems.Sum(item => (item.Price ?? 0) * (item.Quantity ?? 0));
        return new Order
        {
            ShopId = shopId,
            PartnerId = shop.PartnerId,
            TransactionId = Guid.NewGuid().ToString(),
            ListItems = listItems,
            Price = totalPrice,
            Notes = nhanhOrder.Description,
            OrderOrigin = TypeOrigin.Nhanhvn,
            StatusOrder = MapOrderStatusFromNhanhStatus(nhanhOrder.StatusCode),
            StatusTransport = MapTransportStatusFromNhanhStatus(nhanhOrder.StatusCode),
            TypePay = MapPaymentTypeFromNhanhOrderDetail(nhanhOrder),
            StatusPay = MapPaymentStatusFromNhanhOrderDetail(nhanhOrder),
            TransportService = MapCarrierToTransportService(nhanhOrder.CarrierCode) ?? TypeTransportService.LCOD,
            TransportPrice = (long)nhanhOrder.CustomerShipFee,
            StatusDelivery = nhanhOrder.Type == "Giao hàng tận nhà" ? TypeDelivery.ExpressDelivery : TypeDelivery.InShop,
            TotalTaxAmount = 0,
            TotalAfterTax = nhanhOrder.CalcTotalMoney,
            TransportOrderId = nhanhOrder.CarrierCode ?? null,
            UserShippingAddress = shippingAddress,
            TrackingUrl = null,
            Creator = shippingAddress,
            Created = DateTime.TryParse(nhanhOrder.CreatedDateTime, out var created) ? created : DateTime.Now,
            Status = TypeStatus.Actived,
            ExternalSource = SyncServiceEnum.NhanhVN,
            ExternalId = nhanhOrder.Id.ToString(),
        };
    }

    /// <summary>
    /// Map dữ liệu từ NhanhProductDetailDto sang ProductDto (cho sản phẩm độc lập)
    /// </summary>
    /// <param name="nhanhProduct">Dữ liệu sản phẩm chi tiết từ Nhanh.vn API</param>
    /// <param name="shopId">ID của shop</param>
    /// <param name="categoryIds">Danh sách category IDs</param>
    /// <returns>ProductDto đã được map</returns>
    public ProductDto MapNhanhProductDetailToProductDto(NhanhProductDetailDto nhanhProduct, string shopId, List<string> categoryIds)
    {
        Shop? shop = _shopRepository.FindByShopId(shopId);
        if (shop == null) return null;

        var warehouse = _warehouseRepository.ListWarehouse(new Paging { PageSize = 1, PageIndex = 0 }, shop.PartnerId, shopId)
            .Result?.FirstOrDefault();

        // Tạo danh sách ảnh
        var imageList = CreateImageList(nhanhProduct);

        // Parse các giá trị số từ string
        var price = decimal.TryParse(nhanhProduct.Price, out var parsedPrice) ? (long)parsedPrice : 0;
        var importPrice = decimal.TryParse(nhanhProduct.ImportPrice, out var parsedImportPrice) ? (long)parsedImportPrice : 0;
        var vat = decimal.TryParse(nhanhProduct.Vat, out var parsedVat) ? parsedVat : 0;
        var width = float.TryParse(nhanhProduct.Width, out var parsedWidth) ? parsedWidth : (float?)null;
        var height = float.TryParse(nhanhProduct.Height, out var parsedHeight) ? parsedHeight : (float?)null;
        var length = float.TryParse(nhanhProduct.Length, out var parsedLength) ? parsedLength : (float?)null;
        var shippingWeight = float.TryParse(nhanhProduct.ShippingWeight, out var parsedWeight) ? parsedWeight : (float?)null;

        return new ProductDto
        {
            PartnerId = shop?.PartnerId,
            ItemsName = nhanhProduct.Name,
            ItemsCode = nhanhProduct.Code,
            ExternalSource = SyncServiceEnum.NhanhVN,
            CategoryIds = categoryIds,
            Price = price,
            PriceReal = price,
            PriceCapital = importPrice,
            Quantity = nhanhProduct.Inventory?.Available ?? 0,
            ItemsInfo = nhanhProduct.Content,
            QuantityPurchase = 0,
            Images = imageList,
            ItemsType = TypeItems.Product,
            ShopId = shopId,
            ItemsHeight = height,
            ItemsWidth = width,
            ItemsLength = length,
            ItemsWeight = shippingWeight,
            CustomTaxRate = vat,
            IsTop = false,
            TypePublish = nhanhProduct.Status == "Active" ? TypePublish.Publish : TypePublish.UnPublish,
            IsShow = nhanhProduct.Status == "Active" || nhanhProduct.Status == "New",
            WarehouseId = warehouse?.WarehouseId,
            IsVariant = false, // Sản phẩm độc lập không có variants
            Created = DateTime.TryParse(nhanhProduct.CreatedDateTime, out var created) ? created : DateTime.Now,
            ListVariant = [], // Sản phẩm độc lập không có variants
        };
    }

    /// <summary>
    /// Map nhóm sản phẩm (cha + variants) từ NhanhProductDetailDto sang ProductDto
    /// </summary>
    /// <param name="productDetails">Dictionary chứa sản phẩm cha và các variants</param>
    /// <param name="shopId">ID của shop</param>
    /// <param name="categoryIds">Danh sách category IDs</param>
    /// <returns>ProductDto với ListVariant chứa tất cả variants</returns>
    public ProductDto MapNhanhProductGroupToProductDto(Dictionary<string, NhanhProductDetailDto> productDetails, string shopId, List<string> categoryIds)
    {
        Shop? shop = _shopRepository.FindByShopId(shopId);
        if (shop == null) return null;

        var warehouse = _warehouseRepository.ListWarehouse(new Paging { PageSize = 1, PageIndex = 0 }, shop.PartnerId, shopId)
            .Result?.FirstOrDefault();

        // Tìm sản phẩm cha (parentId = -2)
        var parentProduct = productDetails.Values.FirstOrDefault(p => p.ParentId == NhanhConstants.ParentProductId);
        if (parentProduct == null) return null;

        // Tìm tất cả variants (parentId > 0)
        var variants = productDetails.Values.Where(p => p.ParentId > 0).ToList();

        // Tạo danh sách ảnh từ sản phẩm cha
        var imageList = CreateImageList(parentProduct);

        // Parse các giá trị số từ sản phẩm cha
        var price = decimal.TryParse(parentProduct.Price, out var parsedPrice) ? (long)parsedPrice : 0;
        var importPrice = decimal.TryParse(parentProduct.ImportPrice, out var parsedImportPrice) ? (long)parsedImportPrice : 0;
        var vat = decimal.TryParse(parentProduct.Vat, out var parsedVat) ? parsedVat : 0;
        var width = float.TryParse(parentProduct.Width, out var parsedWidth) ? parsedWidth : (float?)null;
        var height = float.TryParse(parentProduct.Height, out var parsedHeight) ? parsedHeight : (float?)null;
        var length = float.TryParse(parentProduct.Length, out var parsedLength) ? parsedLength : (float?)null;
        var shippingWeight = float.TryParse(parentProduct.ShippingWeight, out var parsedWeight) ? parsedWeight : (float?)null;

        // Tạo ListVariant từ các variants
        var listVariant = new List<VariantBase>();
        foreach (var variant in variants)
        {
            var variantPrice = decimal.TryParse(variant.Price, out var vPrice) ? (long)vPrice : price;
            var variantImportPrice = decimal.TryParse(variant.ImportPrice, out var vImportPrice) ? (long)vImportPrice : importPrice;
            var variantImageList = CreateImageList(variant);

            listVariant.Add(new VariantBase
            {
                Price = variantPrice,
                PriceReal = variantPrice,
                PriceCapital = variantImportPrice,
                Quantity = variant.Inventory?.Available ?? 0,
                VariantImage = variantImageList.Count > 0 ? variantImageList[0] : (imageList.Count > 0 ? imageList[0] : null),
                VariantNameOne = GetAttributeName(variant.Attributes, 0),
                VariantValueOne = GetAttributeValue(variant.Attributes, 0),
                VariantNameTwo = GetAttributeName(variant.Attributes, 1),
                VariantValueTwo = GetAttributeValue(variant.Attributes, 1),
                VariantNameThree = GetAttributeName(variant.Attributes, 2),
                VariantValueThree = GetAttributeValue(variant.Attributes, 2)
            });
        }

        return new ProductDto
        {
            PartnerId = shop?.PartnerId,
            ItemsName = parentProduct.Name,
            ItemsCode = parentProduct.Code,
            ExternalSource = SyncServiceEnum.NhanhVN,
            CategoryIds = categoryIds,
            Price = price,
            PriceReal = price,
            PriceCapital = importPrice,
            Quantity = variants.Sum(v => v.Inventory?.Available ?? 0),
            ItemsInfo = parentProduct.Content,
            QuantityPurchase = 0,
            Images = imageList,
            ItemsType = TypeItems.Product,
            ShopId = shopId,
            ItemsHeight = height,
            ItemsWidth = width,
            ItemsLength = length,
            ItemsWeight = shippingWeight,
            CustomTaxRate = vat,
            IsTop = false,
            TypePublish = parentProduct.Status == "Active" ? TypePublish.Publish : TypePublish.UnPublish,
            IsShow = parentProduct.Status == "Active" || parentProduct.Status == "New",
            WarehouseId = warehouse?.WarehouseId,
            IsVariant = listVariant.Count > 0,
            Created = DateTime.TryParse(parentProduct.CreatedDateTime, out var created) ? created : DateTime.Now,
            ListVariant = listVariant,
        };
    }

    /// <summary>
    /// Tạo danh sách ảnh từ NhanhProductDetailDto
    /// </summary>
    private List<MediaInfo> CreateImageList(NhanhProductDetailDto nhanhProduct)
    {
        var imageList = new List<MediaInfo>();

        // Thêm ảnh đại diện nếu có
        if (!string.IsNullOrEmpty(nhanhProduct.Image))
        {
            imageList.Add(new MediaInfo
            {
                MediaFileId = Guid.NewGuid().ToString(),
                Type = TypeMedia.IMAGE,
                Link = nhanhProduct.Image
            });
        }

        // Thêm các ảnh khác nếu có
        if (nhanhProduct.Images != null && nhanhProduct.Images.Count > 0)
        {
            foreach (var img in nhanhProduct.Images)
            {
                if (!string.IsNullOrEmpty(img))
                {
                    imageList.Add(new MediaInfo
                    {
                        MediaFileId = Guid.NewGuid().ToString(),
                        Type = TypeMedia.IMAGE,
                        Link = img
                    });
                }
            }
        }

        return imageList;
    }

    /// <summary>
    /// Lấy tên attribute từ danh sách attributes theo index
    /// Cấu trúc: [{"3": {"attributeName": "Màu", "name": "TRẮNG"}}, {"4": {"attributeName": "SIZE", "name": "XL"}}]
    /// </summary>
    private string GetAttributeName(List<Dictionary<string, NhanhProductDetailAttributeDto>> attributes, int index)
    {
        if (attributes == null || index >= attributes.Count || attributes[index] == null)
            return null;

        var attributeDict = attributes[index];
        var firstAttribute = attributeDict.Values.FirstOrDefault();
        return firstAttribute?.AttributeName;
    }

    /// <summary>
    /// Lấy giá trị attribute từ danh sách attributes theo index
    /// Cấu trúc: [{"3": {"attributeName": "Màu", "name": "TRẮNG"}}, {"4": {"attributeName": "SIZE", "name": "XL"}}]
    /// </summary>
    private string GetAttributeValue(List<Dictionary<string, NhanhProductDetailAttributeDto>> attributes, int index)
    {
        if (attributes == null || index >= attributes.Count || attributes[index] == null)
            return null;

        var attributeDict = attributes[index];
        var firstAttribute = attributeDict.Values.FirstOrDefault();
        return firstAttribute?.Name;
    }

    /// <summary>
    /// Tạo sản phẩm mới từ ProductDto
    /// </summary>
    private async Task CreateNewProduct(ProductDto productDto, string itemsCode, string externalId)
    {
        var items = _mapper.Map<Items>(productDto);
        items.ItemsId = Guid.NewGuid().ToString();
        items.ItemsCode = itemsCode;
        items.ItemsType = TypeItems.Product;
        items.Status = TypeStatus.Actived;
        items.Created = DateTime.Now;
        items.ExternalSource = SyncServiceEnum.NhanhVN;
        items.ExternalId = externalId;
        _itemsRepository.CreateItems(items);
    }

    /// <summary>
    /// Cập nhật sản phẩm hiện có từ ProductDto
    /// </summary>
    private async Task UpdateExistingProduct(Items existingItem, ProductDto productDto)
    {
        _itemsFlow.UpdateProductFields(existingItem, productDto);
        existingItem.Updated = DateTime.UtcNow;
        _itemsRepository.UpdateItems(existingItem);
    }

    /// <summary>
    /// Tạo nhóm sản phẩm mới (sản phẩm cha + variants)
    /// </summary>
    private Task CreateNewProductGroup(ProductDto productDto, string itemsCode, string externalId)
    {
        if (productDto.IsVariant == false || productDto.ListVariant == null || productDto.ListVariant.Count == 0)
        {
            // Tạo sản phẩm đơn giản (không có variant)
            var items = _mapper.Map<Items>(productDto);
            items.ItemsId = Guid.NewGuid().ToString();
            items.ItemsCode = itemsCode;
            items.ItemsType = TypeItems.Product;
            items.Status = TypeStatus.Actived;
            items.Created = DateTime.Now;
            items.ExternalSource = SyncServiceEnum.NhanhVN;
            items.ExternalId = externalId;
            _itemsRepository.CreateItems(items);
        }
        else
        {
            // Tạo sản phẩm có variant - tạo một Items cho mỗi variant
            foreach (var variant in productDto.ListVariant)
            {
                var items = _mapper.Map<Items>(productDto);
                items.ItemsId = Guid.NewGuid().ToString();
                items.ItemsCode = itemsCode;
                items.ItemsType = TypeItems.Product;
                items.Status = TypeStatus.Actived;
                items.Created = DateTime.Now;
                items.ExternalSource = SyncServiceEnum.NhanhVN;
                items.ExternalId = externalId;

                // Cập nhật thông tin variant cho Items này
                _itemsFlow.UpdateVariantFields(items, variant);

                _itemsRepository.CreateItems(items);
            }
        }
        return Task.CompletedTask;
    }

    /// <summary>
    /// Cập nhật nhóm sản phẩm hiện có (sản phẩm cha + variants)
    /// </summary>
    private Task UpdateExistingProductGroup(ItemsGroupBy existingGroupItems, ProductDto productDto, string itemsCode)
    {
        if (productDto.IsVariant == false || productDto.ListVariant == null || productDto.ListVariant.Count == 0)
        {
            // Cập nhật sản phẩm đơn giản
            var existingItem = _itemsRepository.FindByItemsId(existingGroupItems.ItemsId);
            if (existingItem != null)
            {
                _itemsFlow.UpdateProductFields(existingItem, productDto);
                existingItem.Updated = DateTime.UtcNow;
                _itemsRepository.UpdateItems(existingItem);
            }
        }
        else
        {
            // Cập nhật sản phẩm có variant
            // Xóa tất cả variants cũ
            var oldVariants = _itemsRepository.FindByItemsCode(itemsCode);
            foreach (var oldVariant in oldVariants)
            {
                _itemsRepository.DeleteItems(oldVariant.ItemsId);
            }

            // Lấy ExternalId từ variant đầu tiên (nếu có)
            string externalId = oldVariants.FirstOrDefault()?.ExternalId ?? "";

            // Tạo lại tất cả variants mới
            foreach (var variant in productDto.ListVariant)
            {
                var items = _mapper.Map<Items>(productDto);
                items.ItemsId = Guid.NewGuid().ToString();
                items.ItemsCode = itemsCode;
                items.ItemsType = TypeItems.Product;
                items.Status = TypeStatus.Actived;
                items.Created = DateTime.Now;
                items.Updated = DateTime.UtcNow;
                items.ExternalSource = SyncServiceEnum.NhanhVN;
                items.ExternalId = externalId;

                // Cập nhật thông tin variant cho Items này
                _itemsFlow.UpdateVariantFields(items, variant);

                _itemsRepository.CreateItems(items);
            }
        }
        return Task.CompletedTask;
    }

    /// <summary>
    /// Map tên nhà vận chuyển từ Nhanh.vn thành mã dịch vụ vận chuyển
    /// </summary>
    /// <returns>Mã dịch vụ vận chuyển</returns>
    public static TypeTransportService? MapCarrierToTransportService(string carrierCode)
    {
        if (string.IsNullOrWhiteSpace(carrierCode))
        {
            return null;
        }

        var normalizedName = carrierCode.Trim().ToLower();

        if (normalizedName.Contains("ahamove")) return TypeTransportService.AHAMOVE;
        if (normalizedName.Contains("j&t") ||
            normalizedName.Contains("jt express") ||
            normalizedName.Contains("jtexpress"))
            return TypeTransportService.JTEXPRESS;
        if (normalizedName.Contains("viettel") || normalizedName.Contains("vtp"))
            return TypeTransportService.VTK;
        if (normalizedName.Contains("vnpost") ||
            normalizedName.Contains("vietnam post"))
            return TypeTransportService.VCN;
        if (normalizedName.Contains("ghn") ||
            normalizedName.Contains("giao hàng nhanh") ||
            normalizedName.Contains("giaohangnhanh"))
            return TypeTransportService.NCOD;
        if (normalizedName.Contains("ghtk") ||
            normalizedName.Contains("giao hàng tiết kiệm"))
            return TypeTransportService.LCOD;
        if (normalizedName.Contains("viettel cargo")) return TypeTransportService.VHT;
        if (normalizedName.Contains("ems")) return TypeTransportService.EMS;
        if (normalizedName.Contains("ninjavan") ||
            normalizedName.Contains("ninja van"))
            return TypeTransportService.NINJAVAN;
        if (normalizedName.Contains("best express") ||
            normalizedName.Contains("bestexpress"))
            return TypeTransportService.BESTEXPRESS;
        if (normalizedName.Contains("post")) return TypeTransportService.PTN;
        if (normalizedName.Contains("phs")) return TypeTransportService.PHS;

        return TypeTransportService.LCOD; // Default fallback
    }

    /// <summary>
    /// Map trạng thái đơn hàng từ statusCode của Nhanh.vn API
    /// </summary>
    /// <param name="statusCode">Mã trạng thái từ Nhanh.vn</param>
    /// <returns>Trạng thái đơn hàng</returns>
    public static TypeOrderStatus MapOrderStatusFromNhanhStatus(string statusCode)
    {
        if (string.IsNullOrEmpty(statusCode))
            return TypeOrderStatus.Pending;

        return statusCode.Trim().ToLower() switch
        {
            "new" or "confirming" or "customerconfirming" or "waitingforpayment" or
            "confirming" => TypeOrderStatus.Pending,
            "confirmed" => TypeOrderStatus.Verified,
            "packing" => TypeOrderStatus.Verified,
            "packed" => TypeOrderStatus.Verified,
            "pickup" => TypeOrderStatus.Verified,
            "shipping" => TypeOrderStatus.Verified,
            "delivered" => TypeOrderStatus.Paid,
            "success" => TypeOrderStatus.Success,
            "canceled" => TypeOrderStatus.Failed,
            "failed" => TypeOrderStatus.Failed,
            "carriercanceled" => TypeOrderStatus.Failed,
            "aborted" => TypeOrderStatus.Failed,
            "soldout" => TypeOrderStatus.Failed,
            "returned" or "returning" => TypeOrderStatus.Refund,
            "refund" or "refunding" => TypeOrderStatus.Refund,
            _ => TypeOrderStatus.Pending
        };
    }

    /// <summary>
    /// Map trạng thái vận chuyển từ statusCode của Nhanh.vn API
    /// </summary>
    /// <param name="statusCode">Mã trạng thái từ Nhanh.vn</param>
    /// <returns>Trạng thái vận chuyển</returns>
    public static TypeTransportStatus MapTransportStatusFromNhanhStatus(string statusCode)
    {
        if (string.IsNullOrEmpty(statusCode))
            return TypeTransportStatus.Created;

        return statusCode.Trim().ToLower() switch
        {
            "new" or "confirming" or "customerconfirming" or "waitingforpayment" or
            "confirming" => TypeTransportStatus.Created,
            "confirmed" => TypeTransportStatus.WaitingForDelivery,
            "packing" => TypeTransportStatus.WaitingForDelivery,
            "packed" => TypeTransportStatus.WaitingForDelivery,
            "pickup" => TypeTransportStatus.WaitingForDelivery,
            "shipping" => TypeTransportStatus.Delivering,
            "delivered" => TypeTransportStatus.Delivering,
            "success" => TypeTransportStatus.Success,
            "canceled" => TypeTransportStatus.Cancel,
            "failed" => TypeTransportStatus.Cancel,
            "carriercanceled" => TypeTransportStatus.Cancel,
            "aborted" => TypeTransportStatus.Cancel,
            "soldout" => TypeTransportStatus.Cancel,
            "returned" or "returning" => TypeTransportStatus.Refunded,
            "refund" or "refunding" => TypeTransportStatus.Refunded,
            _ => TypeTransportStatus.Created
        };
    }

    /// <summary>
    /// Map loại thanh toán từ dữ liệu đơn hàng đầy đủ Nhanh.vn
    /// </summary>
    /// <param name="nhanhOrder">Dữ liệu đơn hàng đầy đủ từ Nhanh.vn</param>
    /// <returns>Loại thanh toán</returns>
    public static TypePayment MapPaymentTypeFromNhanhOrderDetail(NhanhOrderDetailDto nhanhOrder)
    {
        // Nếu có tiền chuyển khoản > 0 thì là Transfer
        if (nhanhOrder.MoneyTransfer > 0)
        {
            return TypePayment.Transfer;
        }

        // Nếu có tiền đặt cọc > 0 thì có thể là Transfer
        if (nhanhOrder.MoneyDeposit > 0)
        {
            return TypePayment.Transfer;
        }

        // Mặc định là COD (thu tiền khi giao hàng)
        return TypePayment.COD;
    }

    /// <summary>
    /// Map trạng thái thanh toán từ dữ liệu đơn hàng đầy đủ Nhanh.vn
    /// </summary>
    /// <param name="nhanhOrder">Dữ liệu đơn hàng đầy đủ từ Nhanh.vn</param>
    /// <returns>Trạng thái thanh toán</returns>
    public static TypePayStatus MapPaymentStatusFromNhanhOrderDetail(NhanhOrderDetailDto nhanhOrder)
    {
        // Nếu có tiền chuyển khoản hoặc đặt cọc thì coi như đã thanh toán
        if (nhanhOrder.MoneyTransfer > 0 || nhanhOrder.MoneyDeposit > 0)
        {
            return TypePayStatus.Paid;
        }

        // Nếu đơn hàng đã hoàn thành/giao thành công và là COD
        if (nhanhOrder.StatusCode?.ToLower() == "success" || nhanhOrder.StatusCode?.ToLower() == "delivered")
        {
            return TypePayStatus.Paid;
        }

        return TypePayStatus.NotPaid;
    }

    /// <summary>
    /// Map trạng thái đơn hàng từ hệ thống sang status của Nhanh.vn
    /// </summary>
    /// <param name="orderStatus">Trạng thái đơn hàng trong hệ thống</param>
    /// <returns>Status string cho Nhanh.vn</returns>
    public static string MapOrderStatusToNhanhStatus(TypeOrderStatus orderStatus)
    {
        return orderStatus switch
        {
            TypeOrderStatus.Pending => "New",
            TypeOrderStatus.Verified => "Confirmed",
            TypeOrderStatus.Paid => "Confirmed",
            TypeOrderStatus.Success => "Success",
            TypeOrderStatus.Failed => "Canceled",
            TypeOrderStatus.Refund => "Return",
            _ => "New"
        };
    }

    /// <summary>
    /// Map trạng thái vận chuyển từ hệ thống sang status của Nhanh.vn
    /// </summary>
    /// <param name="transportStatus">Trạng thái vận chuyển trong hệ thống</param>
    /// <returns>Status string cho Nhanh.vn</returns>
    public static string MapTransportStatusToNhanhStatus(TypeTransportStatus transportStatus)
    {
        return transportStatus switch
        {
            TypeTransportStatus.Created => "New",
            TypeTransportStatus.Verified => "Confirmed",
            TypeTransportStatus.WaitingForDelivery => "WaitingForDelivery",
            TypeTransportStatus.Delivered => "Delivered",
            TypeTransportStatus.Transporting => "Delivering",
            TypeTransportStatus.Delivering => "Delivering",
            TypeTransportStatus.Success => "Success",
            TypeTransportStatus.Waiting => "WaitingForDelivery",
            TypeTransportStatus.Refunding => "WaitingForReturn",
            TypeTransportStatus.Refunded => "Return",
            TypeTransportStatus.Cancel => "Canceled",
            _ => "New"
        };
    }

    /// <summary>
    /// Map loại thanh toán từ hệ thống sang payment method của Nhanh.vn
    /// </summary>
    /// <param name="paymentType">Loại thanh toán trong hệ thống</param>
    /// <returns>Payment method string cho Nhanh.vn</returns>
    public static string MapPaymentTypeToNhanhPaymentMethod(TypePayment paymentType)
    {
        return paymentType switch
        {
            TypePayment.COD => "COD",
            TypePayment.Transfer => "Online",
            TypePayment.Vnpay => "Online",
            TypePayment.Momo => "Online",
            TypePayment.Zalo => "Online",
            TypePayment.Cash => "COD",
            TypePayment.Sepay => "Online",
            TypePayment.Other => "COD",
            _ => "COD"
        };
    }
}